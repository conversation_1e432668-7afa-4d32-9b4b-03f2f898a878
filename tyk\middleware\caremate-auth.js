// CareMate Authorization Middleware for Tyk Gateway
// Handles permission-based authorization with enhanced security
// Includes header encryption, request signing, and timestamp validation

var CareMateAuth = new TykJS.TykMiddleware.NewMiddleware({});

// Security configuration
var HEADER_ENCRYPTION_KEY = "12345678901234567890123456789012";
var REQUEST_SIGNING_SECRET = "SECURE_REQUEST_SIGNING_SECRET_456789";
var TIMESTAMP_TOLERANCE = 300; // 5 minutes

// Crypto functions for security
function encrypt(text, key) {
    // Simple but secure encryption for Tyk middleware
    var nonce = Math.random().toString(36).substring(2, 10);
    var combined = nonce + text;
    var result = '';

    for (var i = 0; i < combined.length; i++) {
        var keyChar = key.charCodeAt(i % key.length);
        var textChar = combined.charCodeAt(i);
        result += String.fromCharCode((textChar + keyChar) % 256);
    }

    // Base64 encode the result
    try {
        return btoa(result);
    } catch (e) {
        // Fallback for characters that can't be base64 encoded
        var base64chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        var encoded = '';
        for (var j = 0; j < result.length; j += 3) {
            var a = result.charCodeAt(j) || 0;
            var b = result.charCodeAt(j + 1) || 0;
            var c = result.charCodeAt(j + 2) || 0;
            var bitmap = (a << 16) | (b << 8) | c;
            encoded += base64chars.charAt((bitmap >> 18) & 63);
            encoded += base64chars.charAt((bitmap >> 12) & 63);
            encoded += base64chars.charAt((bitmap >> 6) & 63);
            encoded += base64chars.charAt(bitmap & 63);
        }
        return encoded;
    }
}

function generateHMAC(data, secret) {
    // Simple hash function for Tyk middleware (limited crypto support)
    var hash = 0;
    var combined = data + secret;
    for (var i = 0; i < combined.length; i++) {
        var char = combined.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
}

// Define permission mappings for different API endpoints
var ENDPOINT_PERMISSIONS = {
    // Facility management
    "GET:/facility": ["view_facilities"],
    "POST:/facility": ["create_facility"],
    "PUT:/facility": ["edit_facility"],
    "DELETE:/facility": ["delete_facility"],

    // Building management
    "GET:/facility/buildings": ["view_buildings"],
    "POST:/facility/buildings": ["create_building"],
    "PUT:/facility/buildings": ["edit_building"],
    "DELETE:/facility/buildings": ["delete_building"],

    // Floor management
    "GET:/facility/floors": ["view_floors"],
    "POST:/facility/floors": ["create_floor"],
    "PUT:/facility/floors": ["edit_floor"],
    "DELETE:/facility/floors": ["delete_floor"],

    // Room management
    "GET:/facility/rooms": ["view_rooms"],
    "POST:/facility/rooms": ["create_room"],
    "PUT:/facility/rooms": ["edit_room"],
    "DELETE:/facility/rooms": ["delete_room"],

    // Identity and role management
    "GET:/identity": ["view_identity"],
    "POST:/identity": ["manage_identity"],
    "PUT:/identity": ["manage_identity"],
    "DELETE:/identity": ["manage_identity"],

    // Profile management
    "GET:/profile": ["view_identity"],
    "PUT:/profile": ["manage_identity"],

    // System management
    "GET:/system": ["view_systems"],
    "POST:/system": ["create_system"],
    "PUT:/system": ["edit_system"],
    "DELETE:/system": ["edit_system"],

    // Watchlist management
    "GET:/watchlist": ["view_watchlist"],
    "POST:/watchlist": ["create_watchlist"],
    "PUT:/watchlist": ["edit_watchlist"],
    "DELETE:/watchlist": ["delete_watchlist"],

    // Access level management
    "GET:/access-level": ["view_access_levels"],
    "POST:/access-level": ["create_access_level"],
    "PUT:/access-level": ["edit_access_level"],
    "DELETE:/access-level": ["edit_access_level"],

    // Appointment management
    "GET:/appointments": ["view_appointments"],
    "POST:/appointments": ["create_guest"],
    "PUT:/appointments": ["update_guest"],
    "DELETE:/appointments": ["delete_guest"],

    // Patient management
    "GET:/patients": ["view_patients"],
    "POST:/patients": ["create_guest"],
    "PUT:/patients": ["update_guest"],
    "DELETE:/patients": ["delete_guest"],

    // Guest management
    "GET:/guests": ["view_guests"],
    "POST:/guests": ["create_guest"],
    "PUT:/guests": ["update_guest"],
    "DELETE:/guests": ["delete_guest"],

    // Visit management
    "GET:/visits": ["view_guests"],
    "POST:/visits": ["create_guest"],
    "PUT:/visits": ["update_guest"],
    "DELETE:/visits": ["delete_guest"],

    // Document management
    "GET:/documents": ["view_document"],
    "POST:/documents": ["create_document"],
    "PUT:/documents": ["edit_document"],
    "DELETE:/documents": ["delete_document"],

    // Training management
    "GET:/training": ["view_training"],
    "POST:/training": ["create_training"],
    "PUT:/training": ["edit_training"],
    "DELETE:/training": ["delete_training"],

    // Vehicle management
    "GET:/vehicles": ["view_vehicle"],
    "POST:/vehicles": ["create_vehicle"],
    "PUT:/vehicles": ["edit_vehicle"],
    "DELETE:/vehicles": ["delete_vehicle"],

    // Kiosk management
    "GET:/kiosk": ["view_guests"],
    "POST:/kiosk": ["create_guest"],
    "PUT:/kiosk": ["update_guest"],

    // Delegate management
    "GET:/delegates": ["view_delegates"],
    "POST:/delegates": ["create_delegate"],
    "PUT:/delegates": ["edit_delegate"],
    "DELETE:/delegates": ["delete_delegate"],

    // Master data endpoints (no specific permissions required)
    "GET:/countries": [],
    "GET:/states": [],
    "GET:/timezones": [],
    "GET:/languages": [],
    "GET:/master-data": [],

    // Site settings
    "GET:/site-setting": ["view_master_data"],
    "POST:/site-setting": ["modify_cache"],
    "PUT:/site-setting": ["modify_cache"],
    "DELETE:/site-setting": ["modify_cache"]
};

CareMateAuth.NewProcessRequest(function(request, session, config) {
    // Log request for debugging (similar to injectHeader.js)
    log("CareMate Auth: Processing request for " + request.RequestURI);

    // Get path and method
    var fullPath = request.RequestURI.split('?')[0];
    var method = request.Method.toUpperCase();

    // Strip the listen path prefix to get the actual API path
    var path = fullPath;
    if (fullPath.indexOf('/caremate/protected/') === 0) {
        path = fullPath.substring('/caremate/protected'.length);
    }

    var endpointKey = method + ":" + path;

    // Extract and validate JWT token from Authorization header
    var authHeaderArray = request.Headers["Authorization"] || request.Headers["authorization"];
    var authHeader = null;
    var identityId = null;
    var permissions = [];

    // Handle header as array (Tyk passes headers as arrays)
    if (authHeaderArray && authHeaderArray.length > 0) {
        authHeader = authHeaderArray[0];
    }

    log("CareMate Auth: Authorization header: " + authHeader);

    if (!authHeader) {
        log("CareMate Auth: No Authorization header found");
        request.ReturnOverrides.ResponseCode = 401;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Authorization header missing",
            error: "MISSING_AUTH_HEADER"
        });
        return CareMateAuth.ReturnData(request, {});
    }

    // Extract token from Bearer format
    var token = null;
    if (authHeader.indexOf("Bearer ") === 0) {
        token = authHeader.substring(7);
    } else {
        log("CareMate Auth: Invalid authorization header format");
        request.ReturnOverrides.ResponseCode = 401;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Invalid authorization header format. Expected: Bearer <token>",
            error: "INVALID_AUTH_FORMAT"
        });
        return CareMateAuth.ReturnData(request, {});
    }

    // Decode JWT payload (basic validation)
    try {
        var tokenParts = token.split('.');
        if (tokenParts.length !== 3) {
            throw new Error("Invalid JWT token structure");
        }

        // Decode JWT payload
        var base64Payload = tokenParts[1];
        // Add padding if needed
        while (base64Payload.length % 4) {
            base64Payload += '=';
        }

        // Try to decode base64 - use simple approach
        var decodedPayload = '';
        try {
            // Try using atob if available
            if (typeof atob !== 'undefined') {
                decodedPayload = atob(base64Payload);
            } else {
                // Manual base64 decode as fallback
                decodedPayload = base64Decode(base64Payload);
            }
            var payload = JSON.parse(decodedPayload);
        } catch (decodeError) {
            throw new Error("Failed to decode JWT payload: " + decodeError.message);
        }

        // Validate token type
        if (payload.type !== 'access') {
            throw new Error("Invalid token type. Expected access token");
        }

        // Check token expiration
        var currentTime = Math.floor(Date.now() / 1000);
        if (payload.exp && payload.exp < currentTime) {
            throw new Error("Token has expired");
        }

        // Extract user information and permissions
        identityId = payload.sub;
        permissions = payload.permissions || [];

        if (!identityId) {
            throw new Error("Token missing identity information");
        }

        log("CareMate Auth: JWT validated - Identity: " + identityId + ", Permissions: " + permissions.length);
    } catch (error) {
        log("CareMate Auth: JWT validation failed: " + error.message);
        request.ReturnOverrides.ResponseCode = 401;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "JWT token validation failed: " + error.message,
            error: "JWT_VALIDATION_ERROR"
        });
        return CareMateAuth.ReturnData(request, {});
    }

    // Check permissions for the requested endpoint
    var requiredPermissions = getRequiredPermissions(method, path);
    log("CareMate Auth: Endpoint " + endpointKey + " requires permissions: [" + requiredPermissions.join(", ") + "]");

    if (requiredPermissions.length > 0) {
        var hasAllPermissions = requiredPermissions.every(function(permission) {
            return permissions.indexOf(permission) !== -1;
        });

        if (!hasAllPermissions) {
            log("CareMate Auth: Access denied for identity " + identityId + " to " + endpointKey +
                ". Required: [" + requiredPermissions.join(", ") + "], Has: [" + permissions.join(", ") + "]");

            request.ReturnOverrides.ResponseCode = 403;
            request.ReturnOverrides.ResponseError = JSON.stringify({
                status: false,
                message: "Insufficient permissions to access this resource",
                error: "INSUFFICIENT_PERMISSIONS",
                required_permissions: requiredPermissions,
                endpoint: endpointKey
            });
            return CareMateAuth.ReturnData(request, {});
        }
    }

    // Generate timestamp and nonce for security
    var timestamp = new Date().toISOString();
    var nonce = Math.random().toString(36).substring(2, 15);

    // Create request signature
    var signatureData = method + "|" + path + "|" + timestamp + "|" + nonce;
    var signature = generateHMAC(signatureData, REQUEST_SIGNING_SECRET);

    // Encrypt sensitive headers
    var encryptedIdentityId = encrypt(identityId, HEADER_ENCRYPTION_KEY);
    var encryptedPermissions = encrypt(JSON.stringify(permissions), HEADER_ENCRYPTION_KEY);

    // Set secure headers for downstream services
    request.SetHeaders['x-caremate-identity-id'] = encryptedIdentityId;
    request.SetHeaders['x-caremate-permissions'] = encryptedPermissions;
    request.SetHeaders['x-caremate-auth-method'] = 'JWT';
    request.SetHeaders['x-caremate-authorized'] = 'true';
    request.SetHeaders['x-caremate-required-permissions'] = JSON.stringify(requiredPermissions);
    request.SetHeaders['x-caremate-endpoint'] = endpointKey;
    request.SetHeaders['x-caremate-timestamp'] = timestamp;
    request.SetHeaders['x-caremate-nonce'] = nonce;
    request.SetHeaders['x-caremate-signature'] = signature;
    request.SetHeaders['x-caremate-gateway-id'] = 'tyk-gateway-v1';


    // Log successful authorization
    log("CareMate Auth: Successfully authorized identity " + identityId + " for " + endpointKey + " with " + permissions.length + " permissions");
    log("CareMate Auth: Returning control to Tyk for request forwarding");

    return CareMateAuth.ReturnData(request, {});
});

// Function to get required permissions for an endpoint
function getRequiredPermissions(method, path) {
    // Normalize path by removing query parameters and trailing slashes
    path = path.split('?')[0].replace(/\/$/, '');

    // Handle parameterized routes (replace UUIDs with placeholders)
    path = path.replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id');

    // Handle numeric IDs
    path = path.replace(/\/\d+/g, '/:id');

    var endpointKey = method + ":" + path;

    // Check if endpoint requires specific permissions
    var requiredPermissions = ENDPOINT_PERMISSIONS[endpointKey];

    // If endpoint not found in mapping, check for partial matches with dynamic segments
    if (requiredPermissions === undefined) {
        for (var key in ENDPOINT_PERMISSIONS) {
            var keyPath = key.split(':')[1];
            var keyMethod = key.split(':')[0];

            if (keyMethod === method) {
                // Check for exact match or pattern match with :id placeholders
                if (pathMatches(path, keyPath)) {
                    requiredPermissions = ENDPOINT_PERMISSIONS[key];
                    break;
                }
            }
        }
    }

    // If still not found, default to requiring authentication but no specific permissions
    if (requiredPermissions === undefined) {
        log("CareMate Auth: No specific permissions defined for " + endpointKey + ", allowing authenticated access");
        requiredPermissions = [];
    }

    return requiredPermissions;
}

// Helper function to check if a path matches a pattern with :id placeholders
function pathMatches(actualPath, patternPath) {
    var actualParts = actualPath.split('/');
    var patternParts = patternPath.split('/');

    if (actualParts.length !== patternParts.length) {
        return false;
    }

    for (var i = 0; i < actualParts.length; i++) {
        if (patternParts[i] !== ':id' && patternParts[i] !== actualParts[i]) {
            return false;
        }
    }

    return true;
}

// Simple base64 decode function for JWT payload
function base64Decode(str) {
    var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    var result = '';
    var i = 0;

    str = str.replace(/[^A-Za-z0-9+/]/g, '');

    while (i < str.length) {
        var encoded1 = chars.indexOf(str.charAt(i++));
        var encoded2 = chars.indexOf(str.charAt(i++));
        var encoded3 = chars.indexOf(str.charAt(i++));
        var encoded4 = chars.indexOf(str.charAt(i++));

        var bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;

        result += String.fromCharCode((bitmap >> 16) & 255);
        if (encoded3 !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
        if (encoded4 !== 64) result += String.fromCharCode(bitmap & 255);
    }

    return result;
}
