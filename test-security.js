#!/usr/bin/env node

/**
 * Security Test Runner for CareMate
 * Runs comprehensive security tests for Tyk-API integration
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🔒 CareMate Security Test Runner');
console.log('='.repeat(50));

// Check if we're in the right directory
const testFile = path.join(__dirname, 'tyk', 'test-security-improvements.js');

console.log('📍 Running security tests...');
console.log(`📁 Test file: ${testFile}`);
console.log('');

// Run the test
const testProcess = spawn('node', [testFile], {
  stdio: 'inherit',
  cwd: __dirname
});

testProcess.on('close', (code) => {
  console.log('');
  if (code === 0) {
    console.log('🎉 All security tests passed!');
  } else {
    console.log('⚠️  Some security tests failed. Please review the output above.');
  }
  process.exit(code);
});

testProcess.on('error', (error) => {
  console.error('❌ Failed to run security tests:', error.message);
  process.exit(1);
});
