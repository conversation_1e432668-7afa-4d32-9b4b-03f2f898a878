const crypto = require('crypto');
const config = require('../config/config');
const logger = require('../config/logger');

/**
 * Tyk Service - Handles all Tyk gateway related security operations
 * Includes encryption, decryption, signature verification, and validation
 */

/**
 * Better hash function using DJB2 algorithm (matches Tyk middleware)
 */
function betterHash(data) {
  let hash = 5381;
  for (let i = 0; i < data.length; i++) {
    hash = ((hash << 5) + hash) + data.charCodeAt(i);
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(16);
}

/**
 * Custom base64 decode that matches Tyk middleware encoding
 */
function customBase64Decode(str) {
  const base64chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  let decoded = '';

  // Remove padding
  str = str.replace(/=/g, '');

  for (let i = 0; i < str.length; i += 4) {
    const encoded1 = base64chars.indexOf(str.charAt(i)) || 0;
    const encoded2 = base64chars.indexOf(str.charAt(i + 1)) || 0;
    const encoded3 = base64chars.indexOf(str.charAt(i + 2)) || 0;
    const encoded4 = base64chars.indexOf(str.charAt(i + 3)) || 0;

    const bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;

    decoded += String.fromCharCode((bitmap >> 16) & 255);
    if (i + 2 < str.length) decoded += String.fromCharCode((bitmap >> 8) & 255);
    if (i + 3 < str.length) decoded += String.fromCharCode(bitmap & 255);
  }

  return decoded;
}

/**
 * Custom base64 encoding that matches Tyk middleware
 */
function customBase64Encode(str) {
  const base64chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  let encoded = '';

  for (let i = 0; i < str.length; i += 3) {
    const a = str.charCodeAt(i) || 0;
    const b = str.charCodeAt(i + 1) || 0;
    const c = str.charCodeAt(i + 2) || 0;

    const bitmap = (a << 16) | (b << 8) | c;

    encoded += base64chars.charAt((bitmap >> 18) & 63);
    encoded += base64chars.charAt((bitmap >> 12) & 63);
    encoded += (i + 1 < str.length) ? base64chars.charAt((bitmap >> 6) & 63) : '=';
    encoded += (i + 2 < str.length) ? base64chars.charAt(bitmap & 63) : '=';
  }

  return encoded;
}

/**
 * Decrypt Tyk header data using improved decryption (matches Tyk middleware)
 * @param {string} encryptedData - Base64 encoded encrypted data
 * @param {string} key - 32-character encryption key
 * @returns {string} - Decrypted text
 */
function decryptTykHeader(encryptedData, key = config.security.headerEncryptionKey) {
  if (!key || key.length !== 32) {
    throw new Error('Encryption key must be exactly 32 characters');
  }

  try {
    // Custom base64 decode
    const encrypted = customBase64Decode(encryptedData);

    // Extract nonce (first 12 characters for improved encryption)
    const nonce = encrypted.substring(0, 12);
    let ciphertext = encrypted.substring(12);

    const keyHash = betterHash(key + nonce);

    // Reverse the multiple rounds of XOR encryption
    for (let round = 2; round >= 0; round--) {
      const roundKey = betterHash(keyHash + round.toString());
      let tempResult = '';

      for (let i = 0; i < ciphertext.length; i++) {
        const keyChar = roundKey.charCodeAt(i % roundKey.length);
        const cipherChar = ciphertext.charCodeAt(i);
        tempResult += String.fromCharCode(cipherChar ^ keyChar);
      }
      ciphertext = tempResult;
    }

    return ciphertext;
  } catch (error) {
    logger.error('Failed to decrypt Tyk header:', error);
    throw new Error('Failed to decrypt data: ' + error.message);
  }
}

/**
 * Encrypt data for Tyk headers using improved encryption (matches Tyk middleware)
 * @param {string} text - Text to encrypt
 * @param {string} key - 32-character encryption key
 * @returns {string} - Base64 encoded encrypted data
 */
function encryptTykHeader(text, key = config.security.headerEncryptionKey) {
  if (!key || key.length !== 32) {
    throw new Error('Encryption key must be exactly 32 characters');
  }

  try {
    // Generate a stronger nonce (matches Tyk middleware)
    let nonce = '';
    for (let i = 0; i < 12; i++) {
      nonce += String.fromCharCode(Math.floor(Math.random() * 94) + 33);
    }

    const keyHash = betterHash(key + nonce);
    let combined = nonce + text;

    // Multiple rounds of XOR encryption
    for (let round = 0; round < 3; round++) {
      const roundKey = betterHash(keyHash + round.toString());
      let tempResult = '';

      for (let i = 0; i < combined.length; i++) {
        const keyChar = roundKey.charCodeAt(i % roundKey.length);
        const textChar = combined.charCodeAt(i);
        tempResult += String.fromCharCode(textChar ^ keyChar);
      }
      combined = tempResult;
    }

    // Custom base64 encoding (matches Tyk middleware)
    return customBase64Encode(combined);
  } catch (error) {
    logger.error('Failed to encrypt Tyk header:', error);
    throw new Error('Failed to encrypt data: ' + error.message);
  }
}

/**
 * Generate simple hash compatible with Tyk middleware (legacy support)
 * @param {string} data - Data to hash
 * @returns {string} - Hex hash
 */
function generateSimpleHash(data) {
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16);
}

/**
 * Improved HMAC-like function using multiple hash rounds (matches Tyk middleware)
 * @param {string} data - Data to sign
 * @param {string} secret - Signing secret
 * @returns {string} - HMAC signature
 */
function generateStrongerHMAC(data, secret) {
  // Create inner and outer keys (simplified HMAC concept)
  let innerKey = '';
  let outerKey = '';

  for (let i = 0; i < 64; i++) {
    const keyByte = secret.charCodeAt(i % secret.length);
    innerKey += String.fromCharCode(keyByte ^ 0x36);
    outerKey += String.fromCharCode(keyByte ^ 0x5C);
  }

  // Inner hash
  const innerHash = betterHash(innerKey + data);

  // Outer hash
  let outerHash = betterHash(outerKey + innerHash);

  // Additional rounds for strength
  for (let round = 0; round < 3; round++) {
    outerHash = betterHash(outerHash + secret + round.toString());
  }

  return outerHash;
}

/**
 * Generate HMAC-SHA256 signature for request
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @param {string} timestamp - ISO timestamp
 * @param {string} body - Request body (optional)
 * @param {string} secret - Signing secret
 * @returns {string} - HMAC signature
 */
function signRequest(method, path, timestamp, body = '', secret = config.security.requestSigningSecret) {
  if (!secret) {
    throw new Error('Request signing secret is required');
  }
  
  const payload = `${method.toUpperCase()}|${path}|${timestamp}|${body}`;
  return crypto.createHmac('sha256', secret).update(payload).digest('hex');
}

/**
 * Verify HMAC-SHA256 signature for request
 * @param {string} signature - Provided signature
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @param {string} timestamp - ISO timestamp
 * @param {string} body - Request body (optional)
 * @param {string} secret - Signing secret
 * @returns {boolean} - True if signature is valid
 */
function verifySignature(signature, method, path, timestamp, body = '', secret = config.security.requestSigningSecret) {
  try {
    const expectedSignature = signRequest(method, path, timestamp, body, secret);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    logger.error('Signature verification failed:', error);
    return false;
  }
}

/**
 * Verify simple hash signature (compatible with Tyk middleware - legacy)
 * @param {string} signature - Provided signature
 * @param {string} data - Data to verify
 * @param {string} secret - Signing secret
 * @returns {boolean} - True if signature is valid
 */
function verifySimpleSignature(signature, data, secret = config.security.requestSigningSecret) {
  try {
    const expectedSignature = generateSimpleHash(data + secret);
    return signature === expectedSignature;
  } catch (error) {
    logger.error('Simple signature verification failed:', error);
    return false;
  }
}

/**
 * Verify stronger HMAC signature (matches improved Tyk middleware)
 * @param {string} signature - Provided signature
 * @param {string} data - Data to verify
 * @param {string} secret - Signing secret
 * @returns {boolean} - True if signature is valid
 */
function verifyStrongerSignature(signature, data, secret = config.security.requestSigningSecret) {
  try {
    const expectedSignature = generateStrongerHMAC(data, secret);
    return signature === expectedSignature;
  } catch (error) {
    logger.error('Stronger signature verification failed:', error);
    return false;
  }
}

/**
 * Validate timestamp to prevent replay attacks
 * @param {string} timestamp - ISO timestamp
 * @param {number} toleranceSeconds - Allowed time difference in seconds
 * @returns {boolean} - True if timestamp is valid
 */
function validateTimestamp(timestamp, toleranceSeconds = config.security.timestampTolerance) {
  try {
    const requestTime = new Date(timestamp).getTime();
    const currentTime = Date.now();
    const timeDiff = Math.abs(currentTime - requestTime) / 1000;
    
    return timeDiff <= toleranceSeconds;
  } catch (error) {
    logger.error('Timestamp validation failed:', error);
    return false;
  }
}

/**
 * Generate secure random nonce
 * @param {number} length - Length of nonce in bytes
 * @returns {string} - Hex encoded nonce
 */
function generateNonce(length = 16) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Validate IP address against allowed list
 * @param {string} clientIP - Client IP address
 * @param {Array} allowedIPs - Array of allowed IP addresses
 * @returns {boolean} - True if IP is allowed
 */
function validateIP(clientIP, allowedIPs = config.security.allowedGatewayIPs) {
  if (!allowedIPs || allowedIPs.length === 0) {
    logger.warn('No IP restrictions configured - allowing all IPs');
    return true; // No IP restrictions configured
  }
  
  // Normalize IPv6 localhost
  const normalizedIP = clientIP === '::1' ? '127.0.0.1' : clientIP;
  
  const isAllowed = allowedIPs.some(allowedIP => {
    // Handle CIDR notation if needed (basic implementation)
    if (allowedIP.includes('/')) {
      // For now, just check exact match - can be enhanced for CIDR
      return false;
    }
    return allowedIP === normalizedIP || allowedIP === clientIP;
  });

  if (!isAllowed) {
    logger.warn(`IP ${clientIP} not in allowed list: ${allowedIPs.join(', ')}`);
  }

  return isAllowed;
}

/**
 * Validate all Tyk security headers
 * @param {Object} headers - Request headers
 * @returns {Object} - Validation result with decrypted data
 */
function validateTykHeaders(headers) {
  const {
    'x-caremate-identity-id': encryptedIdentityId,
    'x-caremate-permissions': encryptedPermissions,
    'x-caremate-authorized': authorized,
    'x-caremate-timestamp': timestamp,
    'x-caremate-nonce': nonce,
    'x-caremate-signature': signature,
    'x-caremate-gateway-id': gatewayId
  } = headers;

  // Check required headers
  if (!encryptedIdentityId || !encryptedPermissions || authorized !== 'true' || 
      !timestamp || !nonce || !signature || gatewayId !== 'tyk-gateway-v1') {
    throw new Error('Invalid or missing Tyk authentication headers');
  }

  // Validate timestamp
  if (!validateTimestamp(timestamp)) {
    throw new Error('Request timestamp is invalid or too old');
  }

  // Decrypt headers
  let identityId, permissions;
  try {
    identityId = decryptTykHeader(encryptedIdentityId);
    permissions = JSON.parse(decryptTykHeader(encryptedPermissions));
  } catch (error) {
    throw new Error('Failed to decrypt Tyk headers: ' + error.message);
  }

  if (!identityId || !Array.isArray(permissions)) {
    throw new Error('Invalid decrypted header data');
  }

  return {
    identityId,
    permissions,
    timestamp,
    nonce,
    signature
  };
}

/**
 * Generate Tyk security headers for outgoing requests (using improved crypto)
 * @param {string} identityId - Identity ID
 * @param {Array} permissions - User permissions
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @returns {Object} - Security headers
 */
function generateTykHeaders(identityId, permissions, method, path) {
  const timestamp = new Date().toISOString();
  const nonce = generateNonce(8);

  // Encrypt sensitive data using improved encryption
  const encryptedIdentityId = encryptTykHeader(identityId);
  const encryptedPermissions = encryptTykHeader(JSON.stringify(permissions));

  // Generate signature using improved HMAC
  const signatureData = `${method}|${path}|${timestamp}|${nonce}`;
  const signature = generateStrongerHMAC(signatureData, config.security.requestSigningSecret);

  return {
    'x-caremate-identity-id': encryptedIdentityId,
    'x-caremate-permissions': encryptedPermissions,
    'x-caremate-authorized': 'true',
    'x-caremate-timestamp': timestamp,
    'x-caremate-nonce': nonce,
    'x-caremate-signature': signature,
    'x-caremate-gateway-id': 'tyk-gateway-v1'
  };
}

module.exports = {
  // Encryption functions
  decryptTykHeader,
  encryptTykHeader,

  // Hash functions
  betterHash,
  generateSimpleHash,
  generateStrongerHMAC,

  // Signature functions
  signRequest,
  verifySignature,
  verifySimpleSignature,
  verifyStrongerSignature,

  // Validation functions
  validateTimestamp,
  validateIP,
  validateTykHeaders,

  // Utility functions
  generateNonce,
  generateTykHeaders,

  // Base64 functions
  customBase64Encode,
  customBase64Decode
};
