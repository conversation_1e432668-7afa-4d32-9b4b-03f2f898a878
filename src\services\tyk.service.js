const crypto = require('crypto');
const config = require('../config/config');
const logger = require('../config/logger');

/**
 * Tyk Service - Handles all Tyk gateway related security operations
 * Includes encryption, decryption, signature verification, and validation
 */

/**
 * Decrypt data using simple but secure decryption (compatible with Tyk middleware)
 * @param {string} encryptedData - Base64 encoded encrypted data
 * @param {string} key - 32-character encryption key
 * @returns {string} - Decrypted text
 */
function decryptTykHeader(encryptedData, key = config.security.headerEncryptionKey) {
  if (!key || key.length !== 32) {
    throw new Error('Encryption key must be exactly 32 characters');
  }
  
  try {
    // Base64 decode
    const decoded = Buffer.from(encryptedData, 'base64').toString('binary');
    let result = '';
    
    // Reverse the encryption process
    for (let i = 0; i < decoded.length; i++) {
      const keyChar = key.charCodeAt(i % key.length);
      const encryptedChar = decoded.charCodeAt(i);
      const originalChar = (encryptedChar - keyChar + 256) % 256;
      result += String.fromCharCode(originalChar);
    }
    
    // Remove the nonce (first 8 characters)
    return result.substring(8);
  } catch (error) {
    logger.error('Failed to decrypt Tyk header:', error);
    throw new Error('Failed to decrypt data: ' + error.message);
  }
}

/**
 * Encrypt data using simple but secure encryption (compatible with Tyk middleware)
 * @param {string} text - Text to encrypt
 * @param {string} key - 32-character encryption key
 * @returns {string} - Base64 encoded encrypted data
 */
function encryptTykHeader(text, key = config.security.headerEncryptionKey) {
  if (!key || key.length !== 32) {
    throw new Error('Encryption key must be exactly 32 characters');
  }
  
  try {
    // Add nonce
    const nonce = Math.random().toString(36).substring(2, 10);
    const combined = nonce + text;
    let result = '';
    
    for (let i = 0; i < combined.length; i++) {
      const keyChar = key.charCodeAt(i % key.length);
      const textChar = combined.charCodeAt(i);
      result += String.fromCharCode((textChar + keyChar) % 256);
    }
    
    // Base64 encode
    return Buffer.from(result, 'binary').toString('base64');
  } catch (error) {
    logger.error('Failed to encrypt Tyk header:', error);
    throw new Error('Failed to encrypt data: ' + error.message);
  }
}

/**
 * Generate simple hash compatible with Tyk middleware
 * @param {string} data - Data to hash
 * @returns {string} - Hex hash
 */
function generateSimpleHash(data) {
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16);
}

/**
 * Generate HMAC-SHA256 signature for request
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @param {string} timestamp - ISO timestamp
 * @param {string} body - Request body (optional)
 * @param {string} secret - Signing secret
 * @returns {string} - HMAC signature
 */
function signRequest(method, path, timestamp, body = '', secret = config.security.requestSigningSecret) {
  if (!secret) {
    throw new Error('Request signing secret is required');
  }
  
  const payload = `${method.toUpperCase()}|${path}|${timestamp}|${body}`;
  return crypto.createHmac('sha256', secret).update(payload).digest('hex');
}

/**
 * Verify HMAC-SHA256 signature for request
 * @param {string} signature - Provided signature
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @param {string} timestamp - ISO timestamp
 * @param {string} body - Request body (optional)
 * @param {string} secret - Signing secret
 * @returns {boolean} - True if signature is valid
 */
function verifySignature(signature, method, path, timestamp, body = '', secret = config.security.requestSigningSecret) {
  try {
    const expectedSignature = signRequest(method, path, timestamp, body, secret);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    logger.error('Signature verification failed:', error);
    return false;
  }
}

/**
 * Verify simple hash signature (compatible with Tyk middleware)
 * @param {string} signature - Provided signature
 * @param {string} data - Data to verify
 * @param {string} secret - Signing secret
 * @returns {boolean} - True if signature is valid
 */
function verifySimpleSignature(signature, data, secret = config.security.requestSigningSecret) {
  try {
    const expectedSignature = generateSimpleHash(data + secret);
    return signature === expectedSignature;
  } catch (error) {
    logger.error('Simple signature verification failed:', error);
    return false;
  }
}

/**
 * Validate timestamp to prevent replay attacks
 * @param {string} timestamp - ISO timestamp
 * @param {number} toleranceSeconds - Allowed time difference in seconds
 * @returns {boolean} - True if timestamp is valid
 */
function validateTimestamp(timestamp, toleranceSeconds = config.security.timestampTolerance) {
  try {
    const requestTime = new Date(timestamp).getTime();
    const currentTime = Date.now();
    const timeDiff = Math.abs(currentTime - requestTime) / 1000;
    
    return timeDiff <= toleranceSeconds;
  } catch (error) {
    logger.error('Timestamp validation failed:', error);
    return false;
  }
}

/**
 * Generate secure random nonce
 * @param {number} length - Length of nonce in bytes
 * @returns {string} - Hex encoded nonce
 */
function generateNonce(length = 16) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Validate IP address against allowed list
 * @param {string} clientIP - Client IP address
 * @param {Array} allowedIPs - Array of allowed IP addresses
 * @returns {boolean} - True if IP is allowed
 */
function validateIP(clientIP, allowedIPs = config.security.allowedGatewayIPs) {
  if (!allowedIPs || allowedIPs.length === 0) {
    logger.warn('No IP restrictions configured - allowing all IPs');
    return true; // No IP restrictions configured
  }
  
  // Normalize IPv6 localhost
  const normalizedIP = clientIP === '::1' ? '127.0.0.1' : clientIP;
  
  const isAllowed = allowedIPs.some(allowedIP => {
    // Handle CIDR notation if needed (basic implementation)
    if (allowedIP.includes('/')) {
      // For now, just check exact match - can be enhanced for CIDR
      return false;
    }
    return allowedIP === normalizedIP || allowedIP === clientIP;
  });

  if (!isAllowed) {
    logger.warn(`IP ${clientIP} not in allowed list: ${allowedIPs.join(', ')}`);
  }

  return isAllowed;
}

/**
 * Validate all Tyk security headers
 * @param {Object} headers - Request headers
 * @returns {Object} - Validation result with decrypted data
 */
function validateTykHeaders(headers) {
  const {
    'x-caremate-identity-id': encryptedIdentityId,
    'x-caremate-permissions': encryptedPermissions,
    'x-caremate-authorized': authorized,
    'x-caremate-timestamp': timestamp,
    'x-caremate-nonce': nonce,
    'x-caremate-signature': signature,
    'x-caremate-gateway-id': gatewayId
  } = headers;

  // Check required headers
  if (!encryptedIdentityId || !encryptedPermissions || authorized !== 'true' || 
      !timestamp || !nonce || !signature || gatewayId !== 'tyk-gateway-v1') {
    throw new Error('Invalid or missing Tyk authentication headers');
  }

  // Validate timestamp
  if (!validateTimestamp(timestamp)) {
    throw new Error('Request timestamp is invalid or too old');
  }

  // Decrypt headers
  let identityId, permissions;
  try {
    identityId = decryptTykHeader(encryptedIdentityId);
    permissions = JSON.parse(decryptTykHeader(encryptedPermissions));
  } catch (error) {
    throw new Error('Failed to decrypt Tyk headers: ' + error.message);
  }

  if (!identityId || !Array.isArray(permissions)) {
    throw new Error('Invalid decrypted header data');
  }

  return {
    identityId,
    permissions,
    timestamp,
    nonce,
    signature
  };
}

/**
 * Generate Tyk security headers for outgoing requests
 * @param {string} identityId - Identity ID
 * @param {Array} permissions - User permissions
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @returns {Object} - Security headers
 */
function generateTykHeaders(identityId, permissions, method, path) {
  const timestamp = new Date().toISOString();
  const nonce = generateNonce(8);
  
  // Encrypt sensitive data
  const encryptedIdentityId = encryptTykHeader(identityId);
  const encryptedPermissions = encryptTykHeader(JSON.stringify(permissions));
  
  // Generate signature
  const signatureData = `${method}|${path}|${timestamp}|${nonce}`;
  const signature = generateSimpleHash(signatureData + config.security.requestSigningSecret);
  
  return {
    'x-caremate-identity-id': encryptedIdentityId,
    'x-caremate-permissions': encryptedPermissions,
    'x-caremate-authorized': 'true',
    'x-caremate-timestamp': timestamp,
    'x-caremate-nonce': nonce,
    'x-caremate-signature': signature,
    'x-caremate-gateway-id': 'tyk-gateway-v1'
  };
}

module.exports = {
  // Encryption functions
  decryptTykHeader,
  encryptTykHeader,
  
  // Signature functions
  generateSimpleHash,
  signRequest,
  verifySignature,
  verifySimpleSignature,
  
  // Validation functions
  validateTimestamp,
  validateIP,
  validateTykHeaders,
  
  // Utility functions
  generateNonce,
  generateTykHeaders
};
