#!/usr/bin/env node

/**
 * Comprehensive Security Test Suite for CareMate Tyk-API Integration
 * Tests JWT validation, improved crypto functions, and end-to-end security flow
 */

const axios = require('axios');
const crypto = require('crypto');

// Configuration
const TYK_PUBLIC_URL = 'http://localhost:8181/caremate/api';
const TYK_PROTECTED_URL = 'http://localhost:8181/caremate/protected';
const API_DIRECT_URL = 'http://localhost:3001/api';

// Test credentials
const TEST_CREDENTIALS = {
  email: "<EMAIL>",
  password: "Pa$$w0rd!"
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

/**
 * Log test result
 */
function logTest(testName, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${details}`);
  }
  testResults.details.push({ testName, passed, details });
}

/**
 * Make HTTP request with error handling
 */
async function makeRequest(method, url, headers = {}, data = null) {
  try {
    const config = {
      method: method.toLowerCase(),
      url: url,
      headers: headers,
      validateStatus: () => true,
      timeout: 10000
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return {
      status: response.status,
      data: response.data,
      headers: response.headers
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      data: null
    };
  }
}

/**
 * Test 1: Login and JWT Token Validation
 */
async function testLogin() {
  console.log('\n=== Test 1: Login and JWT Token Validation ===');
  
  const response = await makeRequest('POST', `${TYK_PUBLIC_URL}/auth/login`, {
    'Content-Type': 'application/json'
  }, TEST_CREDENTIALS);

  if (response.status === 200 && response.data.status) {
    const token = response.data.data.tokens.access.token;
    const permissions = response.data.data.permissions || [];
    
    logTest('Login successful', true);
    logTest('JWT token received', !!token);
    logTest('Permissions included', permissions.length > 0);
    
    // Validate JWT structure
    const tokenParts = token.split('.');
    logTest('JWT has correct structure', tokenParts.length === 3);
    
    // Decode and validate payload
    try {
      const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
      logTest('JWT payload decodable', true);
      logTest('JWT has identity (sub)', !!payload.sub);
      logTest('JWT has permissions', Array.isArray(payload.permissions));
      logTest('JWT has expiration', !!payload.exp);
      logTest('JWT is access token', payload.type === 'access');
    } catch (error) {
      logTest('JWT payload validation', false, error.message);
    }
    
    return token;
  } else {
    logTest('Login failed', false, `Status: ${response.status}`);
    return null;
  }
}

/**
 * Test 2: Tyk Native JWT Validation
 */
async function testTykJWTValidation(token) {
  console.log('\n=== Test 2: Tyk Native JWT Validation ===');
  
  if (!token) {
    logTest('Tyk JWT validation', false, 'No token available');
    return;
  }

  // Test with valid token
  const validResponse = await makeRequest('GET', `${TYK_PROTECTED_URL}/profile/language`, {
    'Authorization': `Bearer ${token}`
  });
  
  logTest('Valid JWT accepted by Tyk', validResponse.status === 200);
  
  // Test with invalid token
  const invalidResponse = await makeRequest('GET', `${TYK_PROTECTED_URL}/profile/language`, {
    'Authorization': `Bearer invalid_token`
  });
  
  logTest('Invalid JWT rejected by Tyk', invalidResponse.status === 401);
  
  // Test without token
  const noTokenResponse = await makeRequest('GET', `${TYK_PROTECTED_URL}/profile/language`);
  
  logTest('Request without JWT rejected', noTokenResponse.status === 401);
}

/**
 * Test 3: Crypto Functions Validation
 */
async function testCryptoFunctions() {
  console.log('\n=== Test 3: Crypto Functions Validation ===');
  
  // Test better hash function
  const testData = "test_data_for_hashing";
  const hash1 = betterHash(testData);
  const hash2 = betterHash(testData);
  const hash3 = betterHash(testData + "different");
  
  logTest('Better hash is consistent', hash1 === hash2);
  logTest('Better hash produces different results for different inputs', hash1 !== hash3);
  logTest('Better hash produces hex output', /^[0-9a-f]+$/i.test(hash1));
  
  // Test improved encryption/decryption
  const testText = "sensitive_data_123";
  const testKey = "12345678901234567890123456789012";
  
  try {
    const encrypted = betterEncrypt(testText, testKey);
    logTest('Improved encryption works', !!encrypted);
    logTest('Encrypted data is base64', /^[A-Za-z0-9+/=]+$/.test(encrypted));
    logTest('Encrypted data is different from original', encrypted !== testText);
    
    // Note: Decryption test would require implementing the reverse function
    logTest('Encryption produces non-empty result', encrypted.length > 0);
  } catch (error) {
    logTest('Improved encryption', false, error.message);
  }
  
  // Test stronger HMAC
  const hmacData = "method|path|timestamp|nonce";
  const hmacSecret = "test_secret_key";
  
  try {
    const hmac1 = generateStrongerHMAC(hmacData, hmacSecret);
    const hmac2 = generateStrongerHMAC(hmacData, hmacSecret);
    const hmac3 = generateStrongerHMAC(hmacData + "different", hmacSecret);
    
    logTest('Stronger HMAC is consistent', hmac1 === hmac2);
    logTest('Stronger HMAC produces different results for different inputs', hmac1 !== hmac3);
    logTest('Stronger HMAC produces hex output', /^[0-9a-f]+$/i.test(hmac1));
  } catch (error) {
    logTest('Stronger HMAC', false, error.message);
  }
}

/**
 * Test 4: End-to-End Security Flow
 */
async function testEndToEndSecurity(token) {
  console.log('\n=== Test 4: End-to-End Security Flow ===');
  
  if (!token) {
    logTest('End-to-end security test', false, 'No token available');
    return;
  }

  // Test protected endpoints with different permission requirements
  const endpoints = [
    { path: '/health', description: 'Health check (no auth)', expectAuth: false },
    { path: '/profile/language', description: 'Profile language (auth only)', expectAuth: true },
    { path: '/facility', description: 'Facility (auth + authorization)', expectAuth: true }
  ];

  for (const endpoint of endpoints) {
    if (endpoint.expectAuth) {
      // Test with valid token
      const authResponse = await makeRequest('GET', `${TYK_PROTECTED_URL}${endpoint.path}`, {
        'Authorization': `Bearer ${token}`
      });
      
      logTest(`${endpoint.description} - with auth`, authResponse.status < 400);
      
      // Test without token
      const noAuthResponse = await makeRequest('GET', `${TYK_PROTECTED_URL}${endpoint.path}`);
      
      logTest(`${endpoint.description} - without auth rejected`, noAuthResponse.status === 401);
    } else {
      // Test public endpoint
      const publicResponse = await makeRequest('GET', `${TYK_PUBLIC_URL}${endpoint.path}`);
      
      logTest(`${endpoint.description} - public access`, publicResponse.status < 400);
    }
  }
}

/**
 * Test 5: Security Headers Validation
 */
async function testSecurityHeaders(token) {
  console.log('\n=== Test 5: Security Headers Validation ===');
  
  if (!token) {
    logTest('Security headers test', false, 'No token available');
    return;
  }

  // Make a request and check if security headers are properly set
  const response = await makeRequest('GET', `${TYK_PROTECTED_URL}/profile/language`, {
    'Authorization': `Bearer ${token}`
  });
  
  if (response.status === 200) {
    logTest('Request successful for header validation', true);
    
    // Check if the API received the request (indirect validation)
    // Since we can't directly inspect headers sent to API, we validate by successful response
    logTest('Security headers processed by API', response.status === 200);
  } else {
    logTest('Security headers validation', false, `Request failed with status ${response.status}`);
  }
}

// Helper functions for crypto testing (simplified versions)
function betterHash(data) {
  let hash = 5381;
  for (let i = 0; i < data.length; i++) {
    hash = ((hash << 5) + hash) + data.charCodeAt(i);
    hash = hash & hash;
  }
  return Math.abs(hash).toString(16);
}

function betterEncrypt(text, key) {
  let nonce = '';
  for (let i = 0; i < 12; i++) {
    nonce += String.fromCharCode(Math.floor(Math.random() * 94) + 33);
  }
  
  const keyHash = betterHash(key + nonce);
  let combined = nonce + text;
  
  for (let round = 0; round < 3; round++) {
    const roundKey = betterHash(keyHash + round.toString());
    let tempResult = '';
    
    for (let i = 0; i < combined.length; i++) {
      const keyChar = roundKey.charCodeAt(i % roundKey.length);
      const textChar = combined.charCodeAt(i);
      tempResult += String.fromCharCode(textChar ^ keyChar);
    }
    combined = tempResult;
  }
  
  return Buffer.from(combined, 'binary').toString('base64');
}

function generateStrongerHMAC(data, secret) {
  let innerKey = '';
  let outerKey = '';
  
  for (let i = 0; i < 64; i++) {
    const keyByte = secret.charCodeAt(i % secret.length);
    innerKey += String.fromCharCode(keyByte ^ 0x36);
    outerKey += String.fromCharCode(keyByte ^ 0x5C);
  }
  
  const innerHash = betterHash(innerKey + data);
  let outerHash = betterHash(outerKey + innerHash);
  
  for (let round = 0; round < 3; round++) {
    outerHash = betterHash(outerHash + secret + round.toString());
  }
  
  return outerHash;
}

/**
 * Main test execution
 */
async function runAllTests() {
  console.log('🔒 CareMate Security Improvements Test Suite');
  console.log('='.repeat(50));
  
  try {
    // Run all tests
    const token = await testLogin();
    await testTykJWTValidation(token);
    await testCryptoFunctions();
    await testEndToEndSecurity(token);
    await testSecurityHeaders(token);
    
    // Print summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 Test Summary');
    console.log(`Total Tests: ${testResults.total}`);
    console.log(`Passed: ${testResults.passed} ✅`);
    console.log(`Failed: ${testResults.failed} ❌`);
    console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
    
    if (testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      testResults.details
        .filter(test => !test.passed)
        .forEach(test => console.log(`  - ${test.testName}: ${test.details}`));
    }
    
    process.exit(testResults.failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests, testResults };
