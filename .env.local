# Application enviorment (local, production, development)
NODE_ENV=local
PORT=3001

# Authentication Mode
# Options: custom (API handles auth) or tyk (Gateway handles auth)
AUTH_MODE=tyk
DEFAULT_SERVER_URL="http://localhost:3001/api"

# Authentication Mode tyk
PROTECTED_SERVER_URL=http://localhost:8181/caremate/protected
PUBLIC_SERVER_URL=http://localhost:8181/caremate/api
TYK_URL=http://localhost:8181
TYK_SECRET=THIS_IS_MY_TYK_KEY_FOR_FUTURE

# Security Configuration
TYK_GATEWAY_IP=127.0.0.1,::1,**********,**********,**********,*************,host.docker.internal
HEADER_ENCRYPTION_KEY=12345678901234567890123456789012
REQUEST_SIGNING_SECRET=SECURE_REQUEST_SIGNING_SECRET_456789
TIMESTAMP_TOLERANCE_SECONDS=300

# Database configs
DB_DIALECT=postgres
DB_LOGGING=true

DB_WRITE_HOST=post.onetalkhub.com
DB_WRITE_USERNAME=postgres
DB_WRITE_PASSWORD="adam=2007"
DB_WRITE_DATABASE=local_new

DB_READ_HOST=post.onetalkhub.com
DB_READ_USERNAME=postgres
DB_READ_PASSWORD="adam=2007"
DB_READ_DATABASE=local_new

# JWT
# JWT secret key
JWT_SECRET=THIS_IS_MY_NEW_KEY_FOR_FUTURE
# Number of minutes after which an access token expires
JWT_ACCESS_EXPIRATION_MINUTES=*********
# Number of days after which a refresh token expires
JWT_REFRESH_EXPIRATION_DAYS=30

# Loggs configurations
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=30d
LOG_FILE_TRANSPORT=true

# SAML settings
SAML_ENTRY_POINT=https://saml-idp.com/sso
SAML_ISSUER=saml-app-issuer
SAML_CERT="-----BEGIN CERTIFICATE-----\n...cert key...\n-----END CERTIFICATE-----"
SAML_CALLBACK_URL=http://localhost:3001/auth/saml/callback

# OpenID Connect settings
OIDC_ISSUER=https://accounts.example.com
OIDC_AUTHORIZATION_URL=https://accounts.example.com/o/oauth2/v2/auth
OIDC_TOKEN_URL=https://oauth2.example.com/token
OIDC_USERINFO_URL=https://openidconnect.example.com/userinfo
OIDC_CLIENT_ID=oidc_client_id
OIDC_CLIENT_SECRET=oidc_client_secret
OIDC_CALLBACK_URL=http://localhost:3001/auth/oidc/callback

# Azure AD settings
AZURE_TENANT_ID=tenant-id
AZURE_CLIENT_ID=azure_client_id
AZURE_CLIENT_SECRET=azure_client_secret
AZURE_CALLBACK_URL=https://localhost:3001/auth/azure/callback

# Message Broker Configuration
MESSAGE_QUEUING=true
RABBITMQ_URL="amqp://guest:guest@localhost:5672"
CONCURRENCY_LIMIT=15
COLLECTION_TIMEOUT=3000

# Caching configuration
CACHE_DRIVER=memory         # Options: 'redis', 'memcached', 'memory'
CACHE_TTL=600               # Time-to-live in seconds

# Redis-specific
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Memcached-specific
MEMCACHED_HOST=localhost:11211

# Email Configs
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER="<EMAIL>"
EMAIL_PASS="hzslarqcpelfpuzd"

# Telnyx Configs
TELNYX_API_KEY="sk_test_4eC2v3j5g7f8d9b6a1c2e3f4g5h6i7j8k9l0m1n2o3p4q5r6s7t8u9v0w1x2y3z4"
TELNYX_PHONE_NUMBER="+14155551212"
