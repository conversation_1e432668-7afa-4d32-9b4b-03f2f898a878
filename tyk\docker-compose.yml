services:
  gateway:
    image: docker.tyk.io/tyk-gateway/tyk-gateway:v5.5.0
    ports:
      - 8181:8181
    networks:
      - tyk
    volumes:
      - ./tyk.standalone.conf:/opt/tyk-gateway/tyk.conf
      - ./apps:/opt/tyk-gateway/apps
      - ./middleware:/opt/tyk-gateway/middleware
    environment:
      - JWT_SECRET=THIS_IS_MY_NEW_KEY_FOR_FUTURE
      - TYK_SECRET=THIS_IS_MY_TYK_KEY_FOR_FUTURE
      - HEADER_ENCRYPTION_KEY=12345678901234567890123456789012
      - REQUEST_SIGNING_SECRET=SECURE_REQUEST_SIGNING_SECRET_456789
    depends_on:
      - redis
    extra_hosts:
      - "host.docker.internal:host-gateway"
  redis:
    image: redis:6.2.7-alpine
    networks:
      - tyk
    ports:
      - 6379:6379

networks:
  tyk:
