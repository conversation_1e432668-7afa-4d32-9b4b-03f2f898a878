#!/bin/bash

# Get JWT token
echo "Getting JWT token..."
TOKEN=$(curl -s -X POST http://localhost:8181/caremate/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Pa$$w0rd!"}' | \
  jq -r '.data.tokens.access.token')

echo "Token: ${TOKEN:0:50}..."

# Test protected endpoint
echo ""
echo "Testing protected endpoint..."
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:8181/caremate/protected/profile/language | jq .

echo ""
echo "Testing facility endpoint..."
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:8181/caremate/protected/facility | jq .
